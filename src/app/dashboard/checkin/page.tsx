import { redirect } from "next/navigation";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import CameraCapture from "@/components/CameraCapture";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export default async function CheckinPage() {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) redirect("/auth/signin");

  return (
    <main className="mx-auto max-w-md px-4 py-4 pb-24">
      {/* Header */}
      <div className="mb-6 flex items-center gap-3">
        <Button asChild variant="ghost" size="icon" className="rounded-full hover:bg-white/20 transition-colors">
          <Link href="/dashboard">
            <ArrowLeft className="h-5 w-5" />
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            New Check-In
          </h1>
          <p className="text-sm text-muted-foreground">
            Daily wellness capture
          </p>
        </div>
      </div>

      {/* Main Card */}
      <Card className="animate-in fade-in-0 slide-in-from-bottom-2 duration-500 border-white/20 shadow-2xl bg-white/90 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <div className="p-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100">
              <ArrowLeft className="h-4 w-4 text-blue-600 rotate-180" />
            </div>
            Live Camera Preview
          </CardTitle>
          <CardDescription className="text-base">
            Position yourself comfortably and capture your daily check-in moment.
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <CameraCapture />
        </CardContent>
      </Card>

      {/* Tips Section */}
      <div className="mt-6 p-4 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100">
        <h3 className="font-semibold text-sm text-blue-900 mb-2">📸 Quick Tips</h3>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• Ensure good lighting for the best photo</li>
          <li>• Use the camera switch button if needed</li>
          <li>• Position your face within the guide circle</li>
        </ul>
      </div>
    </main>
  );
}

