import { redirect } from "next/navigation";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Search, Users } from "lucide-react";
import Link from "next/link";
import SearchUsers from "@/components/SearchUsers";
import FollowingList from "@/components/FollowingList";
import UsernameDisplay from "@/components/UsernameDisplay";
import { FollowingProvider } from "@/contexts/FollowingContext";

export default async function DiscoverPage() {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) redirect("/auth/signin");

  return (
    <FollowingProvider>
      <main className="mx-auto max-w-2xl px-4 py-6">
        <Card className="mb-6 overflow-hidden bg-[linear-gradient(to_bottom,_#f7d488_0%,_#fbfbea_22%,_#e7f7ee_38%,_#a8d5c8_60%,_#2a4a5e_86%,_#193043_100%)] shadow-md">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Discover People
              </CardTitle>
              <CardDescription>Find and follow other users</CardDescription>
            </div>
            <Button asChild size="sm" variant="outline" className="rounded-full bg-white/20 hover:bg-white/30">
              <Link href="/social" className="flex items-center gap-1">
                <ArrowLeft className="h-4 w-4" />
                Back to Feed
              </Link>
            </Button>
          </CardHeader>
        </Card>

        <div className="space-y-6">
          {/* Username Display */}
          <UsernameDisplay />

          {/* Search Section */}
          <Card className="overflow-hidden bg-gradient-to-br from-white/90 to-white/70 dark:from-white/10 dark:to-white/5 border-white/40 dark:border-white/10 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg">Search Users</CardTitle>
              <CardDescription>Search for users by their username</CardDescription>
            </CardHeader>
            <CardContent>
              <SearchUsers />
            </CardContent>
          </Card>

          {/* Following List */}
          <FollowingList type="following" title="People You Follow" />

          {/* Followers List */}
          <FollowingList type="followers" title="Your Followers" />
        </div>
      </main>
    </FollowingProvider>
  );
}
