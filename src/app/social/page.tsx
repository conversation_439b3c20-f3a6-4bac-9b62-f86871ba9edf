import { redirect } from "next/navigation";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Heart, MessageCircle, Users, UserPlus } from "lucide-react";
import Link from "next/link";
import { formatVietnamDateTime } from "@/lib/utils";
import { createDrizzle } from "@/config/database";

interface SocialCheckin {
  id: string;
  photoUrl: string;
  createdAt: string;
  location?: string;
  user: {
    id: string;
    email: string;
    displayName: string;
  };
}

export default async function SocialPage({
  searchParams,
}: {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) redirect("/auth/signin");

  const resolvedSearchParams = (await searchParams) ?? {};
  const page = typeof resolvedSearchParams.page === "string" ? parseInt(resolvedSearchParams.page, 10) : 1;
  const limit = 10;
  const offset = (page - 1) * limit;

  const { client } = createDrizzle();
  await client.connect();

  try {
    // Get social feed
    const { rows: checkins } = await client.query(
      `SELECT
        c.id,
        c.photo_url,
        c.created_at,
        c.location,
        c.user_id,
        p.display_name
       FROM checkins c
       JOIN follows f ON c.user_id = f.following_id
       LEFT JOIN profiles p ON c.user_id = p.id
       WHERE f.follower_id = $1
       ORDER BY c.created_at DESC
       LIMIT $2 OFFSET $3`,
      [user.id, limit, offset]
    );

    // Get total count
    const { rows: countRows } = await client.query(
      `SELECT COUNT(*)::int as total
       FROM checkins c
       JOIN follows f ON c.user_id = f.following_id
       WHERE f.follower_id = $1`,
      [user.id]
    );

    // Get following count
    const { rows: followingCountRows } = await client.query(
      `SELECT COUNT(*)::int as count FROM follows WHERE follower_id = $1`,
      [user.id]
    );

    const total = countRows[0]?.total || 0;
    const followingCount = followingCountRows[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const socialCheckins: SocialCheckin[] = checkins.map(row => ({
      id: row.id,
      photoUrl: row.photo_url,
      createdAt: row.created_at,
      location: row.location,
      user: {
        id: row.user_id,
        email: '<EMAIL>', // Placeholder - will be populated when profiles have emails
        displayName: row.display_name || `User ${row.user_id.slice(0, 8)}`,
      },
    }));

    await client.end();

    return (
      <main className="mx-auto max-w-2xl px-4 py-6">
        <Card className="mb-6 overflow-hidden bg-[linear-gradient(to_bottom,_#f7d488_0%,_#fbfbea_22%,_#e7f7ee_38%,_#a8d5c8_60%,_#2a4a5e_86%,_#193043_100%)] shadow-md">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Social Feed
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Check-ins from {followingCount} people you follow
              </p>
            </div>
            <Button asChild size="sm" className="rounded-full">
              <Link href="/social/discover" className="flex items-center gap-1">
                <UserPlus className="h-4 w-4" />
                Discover
              </Link>
            </Button>
          </CardHeader>
        </Card>

        {socialCheckins.length > 0 ? (
          <div className="space-y-6">
            {socialCheckins.map((checkin, index) => (
              <Card 
                key={checkin.id} 
                className="overflow-hidden bg-gradient-to-br from-white/90 to-white/70 dark:from-white/10 dark:to-white/5 border-white/40 dark:border-white/10 shadow-lg animate-in fade-in-0 slide-in-from-bottom-2 duration-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-sm">{checkin.user.displayName}</h3>
                      <p className="text-xs text-muted-foreground">{checkin.user.email}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-muted-foreground">
                        {formatVietnamDateTime(checkin.createdAt)}
                      </p>
                      {checkin.location && (
                        <p className="text-xs text-muted-foreground">📍 {checkin.location}</p>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="relative aspect-square overflow-hidden rounded-lg mb-3">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={checkin.photoUrl}
                      alt="Check-in"
                      className="h-full w-full object-cover transition-transform hover:scale-105"
                    />
                  </div>
                  <div className="flex items-center gap-4 text-muted-foreground">
                    <Button variant="ghost" size="sm" className="h-8 px-2 rounded-full">
                      <Heart className="h-4 w-4 mr-1" />
                      <span className="text-xs">Like</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 px-2 rounded-full">
                      <MessageCircle className="h-4 w-4 mr-1" />
                      <span className="text-xs">Comment</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 pt-4">
                {hasPrev && (
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/social?page=${page - 1}`}>Previous</Link>
                  </Button>
                )}
                <span className="text-sm text-muted-foreground">
                  Page {page} of {totalPages}
                </span>
                {hasNext && (
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/social?page=${page + 1}`}>Next</Link>
                  </Button>
                )}
              </div>
            )}
          </div>
        ) : (
          <Card className="text-center py-12 bg-gradient-to-br from-white/90 to-white/70 dark:from-white/10 dark:to-white/5 border-white/40 dark:border-white/10">
            <CardContent>
              <Heart className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-medium mb-2">Your social feed is empty</h3>
              <p className="text-muted-foreground mb-6">
                {followingCount === 0 
                  ? "Start following people to see their check-ins here!"
                  : "The people you follow haven't checked in recently."
                }
              </p>
              <Button asChild className="rounded-full">
                <Link href="/social/discover" className="flex items-center gap-2">
                  <UserPlus className="h-4 w-4" />
                  Discover People
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </main>
    );
  } catch (error) {
    console.error("Social page error:", error);
    await client.end();
    return (
      <main className="mx-auto max-w-2xl px-4 py-6">
        <Card className="text-center py-12">
          <CardContent>
            <p className="text-muted-foreground">Something went wrong. Please try again later.</p>
          </CardContent>
        </Card>
      </main>
    );
  }
}
