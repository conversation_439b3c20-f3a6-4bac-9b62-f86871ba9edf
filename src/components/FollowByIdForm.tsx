"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { User<PERSON><PERSON>, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

export default function FollowByIdForm() {
  const [userId, setUserId] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleFollow = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId.trim()) {
      toast.error("Please enter a user ID");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/follows", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ followingId: userId.trim() }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("User followed successfully!");
        setUserId("");
      } else {
        toast.error(data.error || "Failed to follow user");
      }
    } catch (error) {
      console.error("Follow error:", error);
      toast.error("Failed to follow user");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="overflow-hidden bg-gradient-to-br from-white/90 to-white/70 dark:from-white/10 dark:to-white/5 border-white/40 dark:border-white/10 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserPlus className="h-5 w-5" />
          Follow by User ID
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          For testing: Enter another user's ID to follow them
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleFollow} className="space-y-4">
          <div>
            <Input
              type="text"
              placeholder="Enter user ID (UUID)"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              className="bg-gradient-to-tr from-white/80 via-white/60 to-white/40 border-white/40 dark:from-white/15 dark:via-white/10 dark:to-white/5 dark:border-white/10"
            />
            <p className="text-xs text-muted-foreground mt-1">
              You can find user IDs in the browser's developer tools or database
            </p>
          </div>
          <Button 
            type="submit" 
            disabled={isLoading || !userId.trim()}
            className="w-full rounded-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Following...
              </>
            ) : (
              <>
                <UserPlus className="h-4 w-4 mr-2" />
                Follow User
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
