"use client";

import { useState, useEffect } from "react";
import { Co<PERSON>, User, Check, Edit, Save, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { createSupabaseBrowserClient } from "@/lib/supabase/client";

export default function UsernameDisplay() {
  const [username, setUsername] = useState<string>("");
  const [editingUsername, setEditingUsername] = useState<string>("");
  const [isEditing, setIsEditing] = useState(false);
  const [copied, setCopied] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const fetchUsername = async () => {
      try {
        const supabase = createSupabaseBrowserClient();
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          // Fetch user's profile to get username
          const response = await fetch('/api/profile');
          const data = await response.json();
          
          if (response.ok && data.profile?.username) {
            setUsername(data.profile.username);
            setEditingUsername(data.profile.username);
          }
        }
      } catch (error) {
        console.error('Error fetching username:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUsername();
  }, []);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(username);
      setCopied(true);
      toast.success("Username copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Failed to copy to clipboard");
    }
  };

  const handleSave = async () => {
    if (!editingUsername.trim()) {
      toast.error("Username cannot be empty");
      return;
    }

    if (editingUsername === username) {
      setIsEditing(false);
      return;
    }

    setSaving(true);
    try {
      const response = await fetch('/api/profile/username', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: editingUsername.trim() }),
      });

      const data = await response.json();

      if (response.ok) {
        setUsername(editingUsername.trim());
        setIsEditing(false);
        toast.success("Username updated successfully!");
      } else {
        toast.error(data.error || "Failed to update username");
      }
    } catch (error) {
      console.error('Error updating username:', error);
      toast.error("Failed to update username");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingUsername(username);
    setIsEditing(false);
  };

  if (loading) {
    return (
      <Card className="overflow-hidden bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800">
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
            <div className="h-8 bg-gray-300 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <User className="h-5 w-5" />
          Your Username
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Others can find and follow you using this username
        </p>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2">
          {isEditing ? (
            <>
              <div className="flex-1 flex items-center gap-2">
                <span className="text-muted-foreground">@</span>
                <Input
                  value={editingUsername}
                  onChange={(e) => setEditingUsername(e.target.value)}
                  className="flex-1 bg-white/50 dark:bg-black/20"
                  placeholder="Enter username"
                  disabled={saving}
                />
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={handleSave}
                disabled={saving}
                className="shrink-0 rounded-full bg-green-100 hover:bg-green-200 dark:bg-green-900/50 dark:hover:bg-green-900/70"
              >
                {saving ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancel}
                disabled={saving}
                className="shrink-0 rounded-full bg-red-100 hover:bg-red-200 dark:bg-red-900/50 dark:hover:bg-red-900/70"
              >
                <X className="h-4 w-4" />
              </Button>
            </>
          ) : (
            <>
              <div className="flex-1 p-3 bg-white/50 dark:bg-black/20 rounded-lg font-mono text-sm break-all">
                @{username}
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={copyToClipboard}
                className="shrink-0 rounded-full bg-white/50 hover:bg-white/70 dark:bg-black/50 dark:hover:bg-black/70"
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsEditing(true)}
                className="shrink-0 rounded-full bg-white/50 hover:bg-white/70 dark:bg-black/50 dark:hover:bg-black/70"
              >
                <Edit className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
